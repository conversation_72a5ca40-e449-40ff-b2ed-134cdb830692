package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.FileUploadResponse;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.utils.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.MediaType;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SpringBootTest
class FileUploadServiceTest {

    private SettingServiceImpl settingService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        settingService = new SettingServiceImpl();
        // Set up any required dependencies for the service
    }

    @Test
    void testFileUtilsMediaFileDetection() {
        // Test image files
        assertTrue(FileUtils.isMediaFile("image/jpeg", "test.jpg"));
        assertTrue(FileUtils.isMediaFile("image/png", "test.png"));
        assertTrue(FileUtils.isMediaFile("video/mp4", "test.mp4"));
        
        // Test non-media files
        assertFalse(FileUtils.isMediaFile("application/pdf", "test.pdf"));
        assertFalse(FileUtils.isMediaFile("text/plain", "test.txt"));
        
        // Test file extension fallback
        assertTrue(FileUtils.isMediaFile(null, "image.jpg"));
        assertTrue(FileUtils.isMediaFile(null, "video.mp4"));
        assertFalse(FileUtils.isMediaFile(null, "document.pdf"));
    }

    @Test
    void testFileUtilsValidFileType() {
        // Test valid media files
        assertTrue(FileUtils.isValidFileType("image/jpeg", "test.jpg"));
        assertTrue(FileUtils.isValidFileType("video/mp4", "test.mp4"));
        
        // Test valid document files
        assertTrue(FileUtils.isValidFileType("application/pdf", "test.pdf"));
        assertTrue(FileUtils.isValidFileType("text/plain", "test.txt"));
        assertTrue(FileUtils.isValidFileType("application/javascript", "test.js"));
        
        // Test invalid files
        assertFalse(FileUtils.isValidFileType("application/exe", "test.exe"));
        assertFalse(FileUtils.isValidFileType("application/unknown", "test.unknown"));
    }

    @Test
    void testFileUtilsSubdirectorySelection() {
        // Test media files go to medias subdirectory
        assertEquals("medias", FileUtils.getFileSubdirectory("image/jpeg", "test.jpg"));
        assertEquals("medias", FileUtils.getFileSubdirectory("video/mp4", "test.mp4"));
        
        // Test non-media files go to files subdirectory
        assertEquals("files", FileUtils.getFileSubdirectory("application/pdf", "test.pdf"));
        assertEquals("files", FileUtils.getFileSubdirectory("text/plain", "test.txt"));
    }

    @Test
    void testFileUtilsModuleValidation() {
        // Test valid modules
        assertNull(FileUtils.validateModule("PLAYBOOK"));
        assertNull(FileUtils.validateModule("LEAD"));
        assertNull(FileUtils.validateModule("OPPORTUNITY"));
        assertNull(FileUtils.validateModule("CUSTOM_MODULE"));
        
        // Test invalid modules
        assertNotNull(FileUtils.validateModule(null));
        assertNotNull(FileUtils.validateModule(""));
        assertNotNull(FileUtils.validateModule("lowercase"));
        assertNotNull(FileUtils.validateModule("Mixed-Case"));
        assertNotNull(FileUtils.validateModule("INVALID-CHARS"));
    }

    @Test
    void testFileUtilsFileNameValidation() {
        // Test valid filenames
        assertNull(FileUtils.validateFileName("test.jpg"));
        assertNull(FileUtils.validateFileName("document.pdf"));
        assertNull(FileUtils.validateFileName("my-file_name.txt"));
        
        // Test invalid filenames
        assertNotNull(FileUtils.validateFileName(null));
        assertNotNull(FileUtils.validateFileName(""));
        assertNotNull(FileUtils.validateFileName("../test.jpg"));
        assertNotNull(FileUtils.validateFileName("test/file.jpg"));
        assertNotNull(FileUtils.validateFileName("test\\file.jpg"));
    }

    @Test
    void testFileUtilsUniqueFileNameGeneration() {
        String fileName1 = FileUtils.generateUniqueFileName("test.jpg");
        String fileName2 = FileUtils.generateUniqueFileName("test.jpg");
        
        // Should generate different names
        assertNotEquals(fileName1, fileName2);
        
        // Should preserve extension
        assertTrue(fileName1.endsWith(".jpg"));
        assertTrue(fileName2.endsWith(".jpg"));
        
        // Should contain timestamp
        assertTrue(fileName1.contains("_"));
        assertTrue(fileName2.contains("_"));
    }

    @Test
    void testFileUtilsDirectoryCreation() throws IOException {
        Path uploadsDir = tempDir.resolve("uploads");
        
        // Test directory creation
        assertTrue(FileUtils.ensureDirectoryExists(uploadsDir, "medias", "PLAYBOOK"));
        assertTrue(Files.exists(uploadsDir.resolve("medias").resolve("playbook")));
        
        assertTrue(FileUtils.ensureDirectoryExists(uploadsDir, "files", "LEAD"));
        assertTrue(Files.exists(uploadsDir.resolve("files").resolve("lead")));
    }

    @Test
    void testFileUtilsFileSizeFormatting() {
        assertEquals("500 B", FileUtils.formatFileSize(500));
        assertEquals("1.0 KB", FileUtils.formatFileSize(1024));
        assertEquals("1.5 KB", FileUtils.formatFileSize(1536));
        assertEquals("1.0 MB", FileUtils.formatFileSize(1024 * 1024));
    }

    // Mock FilePart for testing
    private FilePart createMockFilePart(String filename, String contentType, byte[] content) {
        FilePart filePart = mock(FilePart.class);
        when(filePart.filename()).thenReturn(filename);
        
        // Mock headers
        org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        if (contentType != null) {
            headers.setContentType(MediaType.parseMediaType(contentType));
        }
        when(filePart.headers()).thenReturn(headers);
        
        // Mock content
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(content);
        when(filePart.content()).thenReturn(Flux.just(dataBuffer));
        
        return filePart;
    }

    @Test
    void testUploadFilesValidation() {
        // Test null files
        Response<FileUploadResponse> response = settingService.uploadFiles(null, "PLAYBOOK", 1L, "en");
        assertEquals(400, response.getStatusCode());
        assertTrue(response.getMessage().contains("No files provided"));
        
        // Test empty files list
        response = settingService.uploadFiles(Arrays.asList(), "PLAYBOOK", 1L, "en");
        assertEquals(400, response.getStatusCode());
        assertTrue(response.getMessage().contains("No files provided"));
        
        // Test invalid module
        FilePart mockFile = createMockFilePart("test.jpg", "image/jpeg", "test content".getBytes());
        response = settingService.uploadFiles(Arrays.asList(mockFile), "invalid-module", 1L, "en");
        assertEquals(400, response.getStatusCode());
        assertTrue(response.getMessage().contains("uppercase letters"));
    }

    @Test
    void testUploadFilesSuccess() {
        // Create mock files
        FilePart imageFile = createMockFilePart("test.jpg", "image/jpeg", "image content".getBytes());
        FilePart pdfFile = createMockFilePart("document.pdf", "application/pdf", "pdf content".getBytes());
        FilePart jsFile = createMockFilePart("script.js", "application/javascript", "js content".getBytes());
        
        List<FilePart> files = Arrays.asList(imageFile, pdfFile, jsFile);
        
        // Note: This test would require setting up the actual file system and mocking
        // the file saving operations. For a complete test, you would need to:
        // 1. Set up a temporary directory structure
        // 2. Mock the file saving operations
        // 3. Verify the correct directory structure is created
        // 4. Verify files are saved with correct names and paths
        
        // This is a placeholder for the actual implementation test
        // Response<FileUploadResponse> response = settingService.uploadFiles(files, "PLAYBOOK", 1L, "en");
        // assertEquals(200, response.getStatusCode());
        // assertEquals(3, response.getData().getSuccessCount());
    }
}
