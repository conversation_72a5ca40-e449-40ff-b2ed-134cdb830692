package com.tti.oh_crm_service.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileUploadResponse {
    private List<UploadedFileInfo> uploadedFiles;
    private Integer successCount;
    private Integer totalCount;
    private List<String> errors;
    private String module;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UploadedFileInfo {
        private String originalFileName;
        private String savedFileName;
        private String filePath;
        private Long fileSize;
        private String contentType;
        private String fileCategory; // "media" or "file"
        private String module;
    }
}
