package com.tti.oh_crm_service.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class FileUtils {

    // Media file extensions (images and videos)
    private static final List<String> MEDIA_EXTENSIONS = Arrays.asList(
        // Images
        ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".svg", ".tiff", ".ico",
        // Videos
        ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv", ".m4v", ".3gp", ".ogv"
    );

    // Media content types (images and videos)
    private static final List<String> MEDIA_CONTENT_TYPES = Arrays.asList(
        // Images
        "image/jpeg", "image/png", "image/gif", "image/webp", "image/bmp", "image/svg+xml", 
        "image/tiff", "image/x-icon", "image/vnd.microsoft.icon",
        // Videos
        "video/mp4", "video/avi", "video/quicktime", "video/x-msvideo", "video/x-flv", 
        "video/webm", "video/x-matroska", "video/3gpp", "video/ogg"
    );

    // Allowed file extensions for general files
    private static final List<String> ALLOWED_FILE_EXTENSIONS = Arrays.asList(
        // Documents
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf", ".odt", ".ods", ".odp",
        // Web files
        ".html", ".htm", ".css", ".js", ".json", ".xml", ".csv",
        // Archives
        ".zip", ".rar", ".7z", ".tar", ".gz",
        // Other common formats
        ".sql", ".log", ".md", ".yaml", ".yml", ".properties", ".ini", ".cfg", ".conf"
    );

    // Allowed content types for general files
    private static final List<String> ALLOWED_FILE_CONTENT_TYPES = Arrays.asList(
        // Documents
        "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "text/plain", "application/rtf", "application/vnd.oasis.opendocument.text",
        "application/vnd.oasis.opendocument.spreadsheet", "application/vnd.oasis.opendocument.presentation",
        // Web files
        "text/html", "text/css", "application/javascript", "text/javascript", "application/json", 
        "application/xml", "text/xml", "text/csv",
        // Archives
        "application/zip", "application/x-rar-compressed", "application/x-7z-compressed",
        "application/x-tar", "application/gzip",
        // Other
        "application/sql", "text/x-log", "text/markdown", "application/x-yaml", "text/yaml",
        "text/x-properties", "text/x-ini"
    );

    /**
     * Determines if a file is a media file (image or video)
     * @param contentType The content type from the file part
     * @param fileName The original filename
     * @return true if the file is a media file
     */
    public static boolean isMediaFile(String contentType, String fileName) {
        // Check content type first
        if (contentType != null) {
            for (String supportedType : MEDIA_CONTENT_TYPES) {
                if (contentType.startsWith(supportedType)) {
                    return true;
                }
            }
        }
        
        // Fallback to file extension check
        if (fileName != null) {
            String lowerFileName = fileName.toLowerCase();
            return MEDIA_EXTENSIONS.stream()
                .anyMatch(lowerFileName::endsWith);
        }
        
        return false;
    }

    /**
     * Validates if the file type is allowed (either media or general file)
     * @param contentType The content type from the file part
     * @param fileName The original filename
     * @return true if the file type is allowed
     */
    public static boolean isValidFileType(String contentType, String fileName) {
        return isMediaFile(contentType, fileName) || isGeneralFile(contentType, fileName);
    }

    /**
     * Determines if a file is a general file (non-media)
     * @param contentType The content type from the file part
     * @param fileName The original filename
     * @return true if the file is a general file
     */
    public static boolean isGeneralFile(String contentType, String fileName) {
        // Check content type first
        if (contentType != null) {
            for (String supportedType : ALLOWED_FILE_CONTENT_TYPES) {
                if (contentType.startsWith(supportedType)) {
                    return true;
                }
            }
        }
        
        // Fallback to file extension check
        if (fileName != null) {
            String lowerFileName = fileName.toLowerCase();
            return ALLOWED_FILE_EXTENSIONS.stream()
                .anyMatch(lowerFileName::endsWith);
        }
        
        return false;
    }

    /**
     * Gets the appropriate subdirectory for a file based on its type
     * @param contentType The content type from the file part
     * @param fileName The original filename
     * @return "medias" for media files, "files" for general files
     */
    public static String getFileSubdirectory(String contentType, String fileName) {
        return isMediaFile(contentType, fileName) ? "medias" : "files";
    }

    /**
     * Extracts the file extension from a filename
     * @param fileName The filename
     * @return The file extension including the dot, or empty string if no extension
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }
        return "";
    }

    /**
     * Extracts the base filename without extension and cleans it for file system compatibility
     * @param fileName The original filename
     * @return The cleaned base filename
     */
    public static String getBaseFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "file";
        }
        
        // Remove file extension
        int lastDotIndex = fileName.lastIndexOf('.');
        String baseName = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
        
        // Replace spaces and special characters with underscores for file system compatibility
        // Keep alphanumeric characters, dots, underscores, and hyphens
        return baseName.replaceAll("[^a-zA-Z0-9._-]", "_");
    }

    /**
     * Generates a unique filename using the format: originalName_timestamp.extension
     * @param originalFileName The original filename
     * @return A unique filename
     */
    public static String generateUniqueFileName(String originalFileName) {
        String fileExtension = getFileExtension(originalFileName);
        String baseFileName = getBaseFileName(originalFileName);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        
        return baseFileName + "_" + timestamp + fileExtension;
    }

    /**
     * Creates the directory structure for uploads if it doesn't exist
     * @param uploadsPath The base uploads path
     * @param subdirectory The subdirectory (medias or files)
     * @param module The module name
     * @return true if directory exists or was created successfully
     */
    public static boolean ensureDirectoryExists(Path uploadsPath, String subdirectory, String module) {
        try {
            Path fullPath = uploadsPath.resolve(subdirectory).resolve(module.toUpperCase());
            log.info("Checking directory: {} (absolute: {})", fullPath, fullPath.toAbsolutePath());

            if (!Files.exists(fullPath)) {
                log.info("Directory does not exist, creating: {}", fullPath.toAbsolutePath());
                Files.createDirectories(fullPath);
                log.info("Successfully created directory: {}", fullPath.toAbsolutePath());
            } else {
                log.info("Directory already exists: {}", fullPath.toAbsolutePath());
            }

            return true;
        } catch (IOException e) {
            log.error("Failed to create directory {}/{}/{}: {}", uploadsPath, subdirectory, module, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Saves a FilePart to the specified path using traditional I/O (no reactive operations)
     * @param filePart The file part to save
     * @param targetPath The target file path
     * @return The size of the saved file, or -1 if failed
     */
    public static long saveFilePart(FilePart filePart, Path targetPath) {
        try (OutputStream outputStream = Files.newOutputStream(targetPath,
                StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING)) {

            // Collect all data buffers and write them synchronously
            DataBuffer dataBuffer = DataBufferUtils.join(filePart.content()).block();
            if (dataBuffer != null) {
                try (InputStream inputStream = dataBuffer.asInputStream()) {
                    inputStream.transferTo(outputStream);
                } finally {
                    DataBufferUtils.release(dataBuffer);
                }
            }

            // Get and return file size
            long fileSize = Files.size(targetPath);
            log.debug("Successfully saved file: {} (size: {} bytes)", targetPath, fileSize);
            return fileSize;
        } catch (Exception e) {
            log.error("Failed to save file {}: {}", targetPath, e.getMessage());
            return -1L;
        }
    }

    /**
     * Validates the filename and returns error message if invalid
     * @param fileName The filename to validate
     * @return null if valid, error message if invalid
     */
    public static String validateFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "File has no name";
        }
        
        // Check for potentially dangerous characters
        if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
            return "File name contains invalid characters";
        }
        
        return null; // Valid
    }

    /**
     * Validates the module name
     * @param module The module name to validate
     * @return null if valid, error message if invalid
     */
    public static String validateModule(String module) {
        if (module == null || module.trim().isEmpty()) {
            return "Module is required";
        }
        
        // Check if module contains only alphanumeric characters and underscores
        if (!module.matches("^[A-Z_]+$")) {
            return "Module must contain only uppercase letters and underscores";
        }
        
        return null; // Valid
    }

    /**
     * Gets the content type from FilePart, with fallback to empty string
     * @param filePart The file part
     * @return The content type or empty string
     */
    public static String getContentType(FilePart filePart) {
        return filePart.headers().getContentType() != null ? 
            filePart.headers().getContentType().toString() : "";
    }

    /**
     * Formats file size in human-readable format
     * @param bytes The file size in bytes
     * @return Formatted file size string
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
