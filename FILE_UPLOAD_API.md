# File Upload API Documentation

## Overview

The File Upload API allows users to upload any file format (images, videos, documents, web files, etc.) organized by modules. Files are automatically categorized and stored in appropriate subdirectories.

## API Endpoint

### Upload Files
```
POST /api/settings/files/upload
```

**Headers:**
- `Authorization: Bearer <jwt-token>` (required)
- `Organization-Id: <organization-id>` (required)
- `Accept-Language: <language>` (optional, defaults to system default)

**Request Body (multipart/form-data):**
- `files`: List of files to upload (required)
- `module`: Module name in uppercase (required, e.g., "PLAYBOOK", "LEAD", "OPPORTUNITY")

**Response:**
```json
{
  "statusCode": 200,
  "message": "All files uploaded successfully",
  "data": {
    "uploadedFiles": [
      {
        "originalFileName": "document.pdf",
        "savedFileName": "document_20241211_143022.pdf",
        "filePath": "uploads/files/playbook/document_20241211_143022.pdf",
        "fileSize": 1024,
        "contentType": "application/pdf",
        "fileCategory": "file",
        "module": "PLAYBOOK"
      }
    ],
    "successCount": 1,
    "totalCount": 1,
    "errors": [],
    "module": "PLAYBOOK"
  }
}
```

## File Organization

Files are automatically organized into the following directory structure:

```
uploads/
├── medias/
│   ├── playbook/
│   ├── lead/
│   ├── opportunity/
│   └── <other-modules>/
└── files/
    ├── playbook/
    ├── lead/
    ├── opportunity/
    └── <other-modules>/
```

### File Categories

**Media Files** (stored in `uploads/medias/<module>/`):
- **Images**: .jpg, .jpeg, .png, .gif, .webp, .bmp, .svg, .tiff, .ico
- **Videos**: .mp4, .avi, .mov, .wmv, .flv, .webm, .mkv, .m4v, .3gp, .ogv

**General Files** (stored in `uploads/files/<module>/`):
- **Documents**: .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .txt, .rtf, .odt, .ods, .odp
- **Web Files**: .html, .htm, .css, .js, .json, .xml, .csv
- **Archives**: .zip, .rar, .7z, .tar, .gz
- **Other**: .sql, .log, .md, .yaml, .yml, .properties, .ini, .cfg, .conf

## File Access

Uploaded files can be accessed directly via HTTP:

```
http://api-dev.ttis.vn/uploads/medias/<module>/<filename>
http://api-dev.ttis.vn/uploads/files/<module>/<filename>
```

**Examples:**
- `http://api-dev.ttis.vn/uploads/medias/playbook/image_20241211_143022.jpg`
- `http://api-dev.ttis.vn/uploads/files/lead/document_20241211_143022.pdf`

## Validation Rules

### Module Names
- Must be uppercase letters and underscores only
- Examples: `PLAYBOOK`, `LEAD`, `OPPORTUNITY`, `CUSTOM_MODULE`
- Invalid: `playbook`, `Lead`, `INVALID-MODULE`

### File Names
- Cannot contain path traversal characters (`../`, `/`, `\`)
- Must have a valid filename

### File Types
- Only supported file types are allowed (see File Categories above)
- File type validation is based on both content type and file extension

## Error Handling

The API returns appropriate error messages for various scenarios:

- **400 Bad Request**: Invalid module name, unsupported file type, invalid filename
- **500 Internal Server Error**: File system errors, directory creation failures

**Error Response Example:**
```json
{
  "statusCode": 400,
  "message": "Some files uploaded successfully",
  "data": {
    "uploadedFiles": [...],
    "successCount": 1,
    "totalCount": 2,
    "errors": [
      "Invalid file type for: malicious.exe. File type not supported."
    ],
    "module": "PLAYBOOK"
  }
}
```

## Backward Compatibility

The existing image upload API (`/api/settings/images/upload`) remains unchanged and fully functional for backward compatibility.

## Testing

Use the provided test script to verify the API functionality:

```bash
./test-file-upload.sh <auth-token> <organization-id> [test-files-directory]
```

## Configuration

### File Size Limits
- Maximum file size: 20MB (configurable in application.properties)
- Maximum request size: 500MB (configurable in application.properties)

### Docker Configuration
The uploads directory is mounted as a volume in Docker:
```yaml
volumes:
  - ./uploads/:/app/uploads/:rw
```

### Nginx Configuration
Nginx serves uploaded files directly with appropriate caching headers and CORS support.

## Security Considerations

1. **File Type Validation**: Only whitelisted file types are allowed
2. **Filename Sanitization**: Dangerous characters are blocked
3. **Path Traversal Protection**: `../` and similar patterns are blocked
4. **Module Validation**: Only valid module names are accepted
5. **Authentication**: JWT token required for all uploads
6. **Organization Isolation**: Files are associated with organization context
